// Real-time Synchronization System
// نظام المزامنة الفورية

class RealTimeSyncManager {
    constructor() {
        this.isEnabled = false;
        this.supabaseClient = null;
        this.subscriptions = [];
        this.fallbackInterval = null;
        this.lastSyncTime = Date.now();
        this.syncCallbacks = {
            products: [],
            categories: [],
            orders: []
        };
        
        console.log('🔄 RealTimeSyncManager initializing...');
        this.init();
    }

    async init() {
        // Wait for Supabase to be initialized
        await this.waitForSupabase();
        
        if (this.supabaseClient) {
            await this.setupRealtimeSubscriptions();
        } else {
            console.warn('⚠️ Supabase not available, using fallback polling');
            this.setupFallbackPolling();
        }
        
        console.log('✅ RealTimeSyncManager initialized');
    }

    async waitForSupabase() {
        return new Promise((resolve) => {
            const checkSupabase = () => {
                if (window.getSupabaseClient && typeof window.getSupabaseClient === 'function') {
                    this.supabaseClient = window.getSupabaseClient();
                    if (this.supabaseClient) {
                        console.log('✅ Supabase client found for real-time sync');
                        resolve();
                        return;
                    }
                }
                
                // Listen for Supabase initialization event
                const handleSupabaseInit = (event) => {
                    this.supabaseClient = event.detail.client;
                    console.log('✅ Supabase client received via event');
                    window.removeEventListener('supabaseInitialized', handleSupabaseInit);
                    resolve();
                };
                
                window.addEventListener('supabaseInitialized', handleSupabaseInit);
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    window.removeEventListener('supabaseInitialized', handleSupabaseInit);
                    console.warn('⚠️ Timeout waiting for Supabase client');
                    resolve();
                }, 10000);
            };
            
            checkSupabase();
        });
    }

    async setupRealtimeSubscriptions() {
        try {
            console.log('🔄 Setting up real-time subscriptions...');
            
            // Subscribe to products changes
            const productsSubscription = this.supabaseClient
                .channel('products_changes')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'products' },
                    (payload) => this.handleProductChange(payload)
                )
                .subscribe();

            // Subscribe to categories changes
            const categoriesSubscription = this.supabaseClient
                .channel('categories_changes')
                .on('postgres_changes',
                    { event: '*', schema: 'public', table: 'categories' },
                    (payload) => this.handleCategoryChange(payload)
                )
                .subscribe();

            // Subscribe to orders changes (for admin dashboard)
            const ordersSubscription = this.supabaseClient
                .channel('orders_changes')
                .on('postgres_changes',
                    { event: '*', schema: 'public', table: 'orders' },
                    (payload) => this.handleOrderChange(payload)
                )
                .subscribe();

            this.subscriptions = [productsSubscription, categoriesSubscription, ordersSubscription];
            this.isEnabled = true;
            
            console.log('✅ Real-time subscriptions established');
        } catch (error) {
            console.error('❌ Failed to setup real-time subscriptions:', error);
            this.setupFallbackPolling();
        }
    }

    setupFallbackPolling() {
        console.log('🔄 Setting up fallback polling mechanism...');
        
        // Poll every 30 seconds
        this.fallbackInterval = setInterval(() => {
            this.pollForChanges();
        }, 30000);
        
        console.log('✅ Fallback polling established');
    }

    async pollForChanges() {
        try {
            const currentTime = Date.now();
            const lastSync = new Date(this.lastSyncTime).toISOString();
            
            // Check for product changes
            if (this.supabaseClient) {
                const { data: products, error: productsError } = await this.supabaseClient
                    .from('products')
                    .select('*')
                    .gte('updated_at', lastSync);

                if (!productsError && products && products.length > 0) {
                    console.log(`📦 Found ${products.length} updated products`);
                    products.forEach(product => {
                        this.handleProductChange({ eventType: 'UPDATE', new: product });
                    });
                }

                // Check for category changes
                const { data: categories, error: categoriesError } = await this.supabaseClient
                    .from('categories')
                    .select('*')
                    .gte('updated_at', lastSync);

                if (!categoriesError && categories && categories.length > 0) {
                    console.log(`📂 Found ${categories.length} updated categories`);
                    categories.forEach(category => {
                        this.handleCategoryChange({ eventType: 'UPDATE', new: category });
                    });
                }
            }
            
            this.lastSyncTime = currentTime;
        } catch (error) {
            console.error('❌ Error during polling:', error);
        }
    }

    handleProductChange(payload) {
        console.log('📦 Product change detected:', payload);
        
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        // Notify all registered callbacks
        this.syncCallbacks.products.forEach(callback => {
            try {
                callback({
                    type: 'product',
                    action: eventType.toLowerCase(),
                    data: newRecord || oldRecord,
                    oldData: oldRecord
                });
            } catch (error) {
                console.error('Error in product sync callback:', error);
            }
        });

        // Update local storage as fallback
        this.updateLocalStorageProducts(eventType, newRecord, oldRecord);
        
        // Trigger global event
        window.dispatchEvent(new CustomEvent('productChanged', {
            detail: { eventType, newRecord, oldRecord }
        }));
    }

    handleCategoryChange(payload) {
        console.log('📂 Category change detected:', payload);
        
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        // Notify all registered callbacks
        this.syncCallbacks.categories.forEach(callback => {
            try {
                callback({
                    type: 'category',
                    action: eventType.toLowerCase(),
                    data: newRecord || oldRecord,
                    oldData: oldRecord
                });
            } catch (error) {
                console.error('Error in category sync callback:', error);
            }
        });
        
        // Trigger global event
        window.dispatchEvent(new CustomEvent('categoryChanged', {
            detail: { eventType, newRecord, oldRecord }
        }));
    }

    handleOrderChange(payload) {
        console.log('📋 Order change detected:', payload);
        
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        // Notify all registered callbacks
        this.syncCallbacks.orders.forEach(callback => {
            try {
                callback({
                    type: 'order',
                    action: eventType.toLowerCase(),
                    data: newRecord || oldRecord,
                    oldData: oldRecord
                });
            } catch (error) {
                console.error('Error in order sync callback:', error);
            }
        });
        
        // Trigger global event
        window.dispatchEvent(new CustomEvent('orderChanged', {
            detail: { eventType, newRecord, oldRecord }
        }));
    }

    updateLocalStorageProducts(eventType, newRecord, oldRecord) {
        try {
            const savedMenu = localStorage.getItem('restaurant_menu_data');
            if (!savedMenu) return;
            
            const menuData = JSON.parse(savedMenu);
            
            if (eventType === 'INSERT' && newRecord) {
                // Add new product to appropriate category
                const categoryKey = this.getCategoryKey(newRecord.category_id);
                if (categoryKey && menuData[categoryKey]) {
                    const menuItem = this.convertToMenuFormat(newRecord);
                    menuData[categoryKey].push(menuItem);
                }
            } else if (eventType === 'UPDATE' && newRecord) {
                // Update existing product
                Object.keys(menuData).forEach(categoryKey => {
                    const items = menuData[categoryKey];
                    const index = items.findIndex(item => item.id === newRecord.id);
                    if (index !== -1) {
                        items[index] = this.convertToMenuFormat(newRecord);
                    }
                });
            } else if (eventType === 'DELETE' && oldRecord) {
                // Remove product
                Object.keys(menuData).forEach(categoryKey => {
                    const items = menuData[categoryKey];
                    const index = items.findIndex(item => item.id === oldRecord.id);
                    if (index !== -1) {
                        items.splice(index, 1);
                    }
                });
            }
            
            localStorage.setItem('restaurant_menu_data', JSON.stringify(menuData));
        } catch (error) {
            console.error('Error updating localStorage:', error);
        }
    }

    convertToMenuFormat(product) {
        return {
            id: product.id,
            name: product.name,
            description: product.description,
            price: parseFloat(product.price),
            category: this.getCategoryKey(product.category_id),
            icon: 'fas fa-utensils', // Default icon
            available: product.is_available
        };
    }

    getCategoryKey(categoryId) {
        // Map category IDs to menu data keys
        const categoryMap = {
            '1': 'appetizers',
            '2': 'main-dishes',
            '3': 'beverages',
            '4': 'desserts'
        };
        return categoryMap[categoryId] || 'main-dishes';
    }

    // Public API methods
    onProductChange(callback) {
        this.syncCallbacks.products.push(callback);
    }

    onCategoryChange(callback) {
        this.syncCallbacks.categories.push(callback);
    }

    onOrderChange(callback) {
        this.syncCallbacks.orders.push(callback);
    }

    removeCallback(type, callback) {
        if (this.syncCallbacks[type]) {
            const index = this.syncCallbacks[type].indexOf(callback);
            if (index > -1) {
                this.syncCallbacks[type].splice(index, 1);
            }
        }
    }

    isRealTimeEnabled() {
        return this.isEnabled;
    }

    destroy() {
        // Clean up subscriptions
        this.subscriptions.forEach(subscription => {
            if (subscription && subscription.unsubscribe) {
                subscription.unsubscribe();
            }
        });
        
        // Clear polling interval
        if (this.fallbackInterval) {
            clearInterval(this.fallbackInterval);
        }
        
        // Clear callbacks
        this.syncCallbacks = { products: [], categories: [], orders: [] };
        
        console.log('🧹 RealTimeSyncManager destroyed');
    }
}

// Create global instance
const realTimeSyncManager = new RealTimeSyncManager();
window.realTimeSyncManager = realTimeSyncManager;

console.log('✅ Real-time sync system loaded');
