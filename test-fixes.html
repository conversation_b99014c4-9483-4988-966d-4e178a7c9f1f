<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات - مطعم محمد الاشرافي</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .test-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-pending { background: #6c757d; }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار إصلاحات مطعم محمد الاشرافي</h1>
        <p>هذه الصفحة تختبر جميع الإصلاحات التي تم تطبيقها على النظام</p>

        <div id="testResults">
            <div class="test-item test-info">
                <h3>🔄 جاري تشغيل الاختبارات...</h3>
                <p>يرجى الانتظار بينما نختبر جميع المكونات</p>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <button onclick="runAllTests()">🔄 إعادة تشغيل الاختبارات</button>
            <button onclick="clearConsole()">🗑️ مسح وحدة التحكم</button>
            <button onclick="window.open('admin/dashboard.html', '_blank')">🎛️ فتح لوحة التحكم</button>
        </div>

        <div class="console-output" id="consoleOutput">
            <strong>وحدة التحكم:</strong><br>
        </div>
    </div>

    <!-- Load all the scripts in the same order as dashboard -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/menu-data.js"></script>
    <script src="js/supabase-loader.js"></script>
    <script src="js/supabase-credentials.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/database-manager.js"></script>

    <script>
        // Capture console output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // Test functions
        const tests = [
            {
                name: 'تحميل menu-data.js',
                test: () => typeof getAllMenuItems === 'function' && typeof getItemById === 'function'
            },
            {
                name: 'تحميل Supabase SDK',
                test: () => typeof window.supabase !== 'undefined' && window.supabase.createClient
            },
            {
                name: 'تحميل supabase-loader',
                test: () => typeof window.supabaseLoader !== 'undefined'
            },
            {
                name: 'تحميل supabase-credentials',
                test: () => typeof window.getSupabaseConfig === 'function'
            },
            {
                name: 'تحميل supabase-config',
                test: () => typeof window.getSupabaseClient === 'function'
            },
            {
                name: 'تحميل database-manager',
                test: () => typeof window.databaseManager !== 'undefined'
            },
            {
                name: 'عمل دالة getAllMenuItems',
                test: () => {
                    try {
                        const items = getAllMenuItems();
                        return Array.isArray(items) && items.length > 0;
                    } catch (e) {
                        return false;
                    }
                }
            },
            {
                name: 'عمل دالة getItemById',
                test: () => {
                    try {
                        const items = getAllMenuItems();
                        if (items.length > 0) {
                            const item = getItemById(items[0].id);
                            return item !== null && item !== undefined;
                        }
                        return false;
                    } catch (e) {
                        return false;
                    }
                }
            }
        ];

        async function runAllTests() {
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = '';

            console.log('🚀 بدء تشغيل الاختبارات...');

            for (const testCase of tests) {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-item';
                
                try {
                    const result = await testCase.test();
                    if (result) {
                        testDiv.className += ' test-success';
                        testDiv.innerHTML = `
                            <h4>✅ ${testCase.name} <span class="status-indicator status-success"></span></h4>
                            <p>الاختبار نجح بشكل صحيح</p>
                        `;
                        console.log(`✅ ${testCase.name}: نجح`);
                    } else {
                        testDiv.className += ' test-error';
                        testDiv.innerHTML = `
                            <h4>❌ ${testCase.name} <span class="status-indicator status-error"></span></h4>
                            <p>الاختبار فشل - يرجى مراجعة الكود</p>
                        `;
                        console.error(`❌ ${testCase.name}: فشل`);
                    }
                } catch (error) {
                    testDiv.className += ' test-error';
                    testDiv.innerHTML = `
                        <h4>❌ ${testCase.name} <span class="status-indicator status-error"></span></h4>
                        <p>خطأ في الاختبار: ${error.message}</p>
                    `;
                    console.error(`❌ ${testCase.name}: خطأ - ${error.message}`);
                }
                
                resultsContainer.appendChild(testDiv);
            }

            // Test database connection
            const dbTestDiv = document.createElement('div');
            dbTestDiv.className = 'test-item';
            
            if (window.databaseManager) {
                const isConnected = window.databaseManager.isConnectedToDatabase();
                if (isConnected) {
                    dbTestDiv.className += ' test-success';
                    dbTestDiv.innerHTML = `
                        <h4>✅ اتصال قاعدة البيانات <span class="status-indicator status-success"></span></h4>
                        <p>متصل بقاعدة البيانات بنجاح</p>
                    `;
                } else {
                    dbTestDiv.className += ' test-warning';
                    dbTestDiv.innerHTML = `
                        <h4>⚠️ اتصال قاعدة البيانات <span class="status-indicator status-warning"></span></h4>
                        <p>غير متصل بقاعدة البيانات - يعمل في وضع fallback</p>
                    `;
                }
            } else {
                dbTestDiv.className += ' test-error';
                dbTestDiv.innerHTML = `
                    <h4>❌ اتصال قاعدة البيانات <span class="status-indicator status-error"></span></h4>
                    <p>لم يتم تحميل database manager</p>
                `;
            }
            
            resultsContainer.appendChild(dbTestDiv);

            console.log('✅ انتهاء الاختبارات');
        }

        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '<strong>وحدة التحكم:</strong><br>';
        }

        // Run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 2000); // Wait 2 seconds for all scripts to load
        });
    </script>
</body>
</html>
