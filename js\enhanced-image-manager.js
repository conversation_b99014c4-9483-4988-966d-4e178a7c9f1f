// Enhanced Image Management System with Supabase Integration
// نظام إدارة الصور المحسن مع تكامل Supabase

class EnhancedImageManager {
    constructor() {
        this.supabaseClient = null;
        this.storageEnabled = false;
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        this.compressionQuality = 0.8;
        this.maxWidth = 1200;
        this.maxHeight = 1200;
        this.thumbnailSize = 300;
        this.uploadQueue = [];
        this.isProcessing = false;
        
        console.log('📸 EnhancedImageManager initializing...');
        this.init();
    }

    async init() {
        await this.waitForSupabase();
        await this.setupStorage();
        this.setupEventListeners();
        this.createUploadStyles();
        
        console.log('✅ EnhancedImageManager initialized');
    }

    async waitForSupabase() {
        return new Promise((resolve) => {
            const checkSupabase = () => {
                if (window.getSupabaseClient && typeof window.getSupabaseClient === 'function') {
                    this.supabaseClient = window.getSupabaseClient();
                    if (this.supabaseClient) {
                        console.log('✅ Supabase client found for enhanced image management');
                        resolve();
                        return;
                    }
                }
                
                // Listen for Supabase initialization event
                const handleSupabaseInit = (event) => {
                    this.supabaseClient = event.detail.client;
                    console.log('✅ Supabase client received for enhanced image management');
                    window.removeEventListener('supabaseInitialized', handleSupabaseInit);
                    resolve();
                };
                
                window.addEventListener('supabaseInitialized', handleSupabaseInit);
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    window.removeEventListener('supabaseInitialized', handleSupabaseInit);
                    console.warn('⚠️ Timeout waiting for Supabase client in EnhancedImageManager');
                    resolve();
                }, 10000);
            };
            
            checkSupabase();
        });
    }

    async setupStorage() {
        if (!this.supabaseClient) {
            console.warn('⚠️ Supabase not available, using local storage for images');
            return;
        }

        try {
            // Test storage access
            const { data, error } = await this.supabaseClient.storage
                .from('restaurant-images')
                .list('', { limit: 1 });

            if (error && error.message.includes('not found')) {
                console.log('🔧 Creating storage bucket...');
                await this.createStorageBucket();
            } else if (error) {
                console.warn('⚠️ Supabase Storage error:', error.message);
            } else {
                this.storageEnabled = true;
                console.log('✅ Supabase Storage ready');
            }
        } catch (error) {
            console.warn('⚠️ Storage setup failed:', error);
        }
    }

    async createStorageBucket() {
        try {
            const { data, error } = await this.supabaseClient.storage
                .createBucket('restaurant-images', {
                    public: true,
                    allowedMimeTypes: this.allowedTypes,
                    fileSizeLimit: this.maxFileSize
                });

            if (error) {
                console.warn('⚠️ Could not create storage bucket:', error.message);
            } else {
                this.storageEnabled = true;
                console.log('✅ Storage bucket created successfully');
            }
        } catch (error) {
            console.warn('⚠️ Bucket creation failed:', error);
        }
    }

    createUploadStyles() {
        if (document.getElementById('enhanced-image-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'enhanced-image-styles';
        styles.textContent = `
            .enhanced-image-upload {
                border: 2px dashed #ddd;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                background: #f9f9f9;
                transition: all 0.3s ease;
                cursor: pointer;
                margin: 15px 0;
            }
            
            .enhanced-image-upload:hover,
            .enhanced-image-upload.drag-over {
                border-color: #007bff;
                background: #f0f8ff;
            }
            
            .enhanced-image-upload i {
                font-size: 2rem;
                color: #007bff;
                margin-bottom: 10px;
            }
            
            .enhanced-image-upload p {
                margin: 10px 0;
                color: #666;
            }
            
            .upload-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-family: 'Cairo', sans-serif;
            }
            
            .upload-btn:hover {
                background: #0056b3;
            }
            
            .image-preview-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            
            .image-preview-item {
                position: relative;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .image-preview-item img {
                width: 100%;
                height: 150px;
                object-fit: cover;
            }
            
            .image-preview-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .image-preview-item:hover .image-preview-overlay {
                opacity: 1;
            }
            
            .image-action-btn {
                background: white;
                border: none;
                padding: 8px;
                margin: 0 4px;
                border-radius: 4px;
                cursor: pointer;
                color: #333;
            }
            
            .image-action-btn:hover {
                background: #f0f0f0;
            }
            
            .image-action-btn.delete {
                color: #dc3545;
            }
            
            .image-action-btn.primary {
                color: #28a745;
            }
            
            .upload-progress {
                background: white;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                margin-bottom: 10px;
            }
            
            .progress-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                font-size: 14px;
            }
            
            .progress-bar {
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }
            
            .progress-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }
            
            .image-message {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-family: 'Cairo', sans-serif;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 8px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .image-message.success { background: #28a745; }
            .image-message.error { background: #dc3545; }
            .image-message.info { background: #17a2b8; }
        `;
        
        document.head.appendChild(styles);
    }

    setupEventListeners() {
        // Handle file input changes
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('enhanced-image-input')) {
                this.handleFileSelection(event.target);
            }
        });

        // Handle drag and drop
        document.addEventListener('dragover', (event) => {
            if (event.target.classList.contains('enhanced-image-upload')) {
                event.preventDefault();
                event.target.classList.add('drag-over');
            }
        });

        document.addEventListener('dragleave', (event) => {
            if (event.target.classList.contains('enhanced-image-upload')) {
                event.target.classList.remove('drag-over');
            }
        });

        document.addEventListener('drop', (event) => {
            if (event.target.classList.contains('enhanced-image-upload')) {
                event.preventDefault();
                event.target.classList.remove('drag-over');
                this.handleFileDrop(event);
            }
        });

        // Handle upload button clicks
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('enhanced-upload-btn')) {
                const input = event.target.parentNode.querySelector('.enhanced-image-input');
                if (input) input.click();
            }
        });
    }

    async handleFileSelection(input) {
        const files = Array.from(input.files);
        const productId = input.dataset.productId;
        
        if (!files.length) return;

        console.log(`📸 Processing ${files.length} files for product ${productId}`);
        
        for (const file of files) {
            this.uploadQueue.push({ file, productId });
        }
        
        this.processUploadQueue();
    }

    async handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        const productId = event.target.dataset.productId;
        
        const imageFiles = files.filter(file => this.allowedTypes.includes(file.type));
        
        if (!imageFiles.length) {
            this.showMessage('يرجى رفع ملفات صور صالحة فقط', 'error');
            return;
        }

        console.log(`📸 Processing ${imageFiles.length} dropped files for product ${productId}`);
        
        for (const file of imageFiles) {
            this.uploadQueue.push({ file, productId });
        }
        
        this.processUploadQueue();
    }

    async processUploadQueue() {
        if (this.isProcessing || this.uploadQueue.length === 0) return;
        
        this.isProcessing = true;
        
        while (this.uploadQueue.length > 0) {
            const { file, productId } = this.uploadQueue.shift();
            await this.processAndUploadImage(file, productId);
        }
        
        this.isProcessing = false;
    }

    async processAndUploadImage(file, productId) {
        try {
            // Validate file
            if (!this.validateFile(file)) {
                return;
            }

            // Show upload progress
            const progressContainer = this.getProgressContainer();
            const progressElement = this.createProgressElement(file.name);
            progressContainer.appendChild(progressElement);

            // Compress and resize image
            this.updateProgress(progressElement, 20, 'ضغط الصورة...');
            const compressedFile = await this.compressImage(file);
            
            // Create thumbnail
            this.updateProgress(progressElement, 40, 'إنشاء الصورة المصغرة...');
            const thumbnailFile = await this.createThumbnail(file);

            // Upload to storage
            this.updateProgress(progressElement, 60, 'رفع الصورة...');
            let imageUrl, thumbnailUrl;
            
            if (this.storageEnabled) {
                imageUrl = await this.uploadToSupabaseStorage(compressedFile, productId, 'full');
                thumbnailUrl = await this.uploadToSupabaseStorage(thumbnailFile, productId, 'thumb');
            } else {
                imageUrl = await this.uploadToLocalStorage(compressedFile, productId, 'full');
                thumbnailUrl = await this.uploadToLocalStorage(thumbnailFile, productId, 'thumb');
            }

            // Save image metadata
            this.updateProgress(progressElement, 80, 'حفظ البيانات...');
            await this.saveImageMetadata({
                productId,
                imageUrl,
                thumbnailUrl,
                fileName: file.name,
                fileSize: compressedFile.size,
                fileType: file.type
            });

            // Complete
            this.updateProgress(progressElement, 100, 'تم بنجاح!');
            
            // Show success message
            this.showMessage(`تم رفع الصورة ${file.name} بنجاح`, 'success');
            
            // Update preview
            this.updateImagePreview(productId);
            
            // Trigger image uploaded event
            window.dispatchEvent(new CustomEvent('imageUploaded', {
                detail: { productId, imageUrl, thumbnailUrl }
            }));

            // Remove progress after delay
            setTimeout(() => {
                if (progressElement.parentNode) {
                    progressElement.parentNode.removeChild(progressElement);
                }
            }, 2000);

        } catch (error) {
            console.error('❌ Error processing image:', error);
            this.showMessage(`فشل في رفع الصورة: ${error.message}`, 'error');
        }
    }

    validateFile(file) {
        if (!this.allowedTypes.includes(file.type)) {
            this.showMessage('نوع الملف غير مدعوم. يرجى رفع صور بصيغة JPG, PNG, أو WebP', 'error');
            return false;
        }

        if (file.size > this.maxFileSize) {
            this.showMessage(`حجم الملف كبير جداً. الحد الأقصى ${this.maxFileSize / 1024 / 1024}MB`, 'error');
            return false;
        }

        return true;
    }

    async compressImage(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = this.calculateDimensions(img.width, img.height, this.maxWidth, this.maxHeight);
                
                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob((blob) => {
                    const compressedFile = new File([blob], file.name, {
                        type: file.type,
                        lastModified: Date.now()
                    });
                    resolve(compressedFile);
                }, file.type, this.compressionQuality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    async createThumbnail(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate thumbnail dimensions (square crop)
                const size = Math.min(img.width, img.height);
                const x = (img.width - size) / 2;
                const y = (img.height - size) / 2;
                
                canvas.width = this.thumbnailSize;
                canvas.height = this.thumbnailSize;

                // Draw thumbnail (cropped to square)
                ctx.drawImage(img, x, y, size, size, 0, 0, this.thumbnailSize, this.thumbnailSize);
                
                canvas.toBlob((blob) => {
                    const thumbnailFile = new File([blob], `thumb_${file.name}`, {
                        type: file.type,
                        lastModified: Date.now()
                    });
                    resolve(thumbnailFile);
                }, file.type, 0.7);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        let width = originalWidth;
        let height = originalHeight;

        // Calculate scaling factor
        const widthRatio = maxWidth / width;
        const heightRatio = maxHeight / height;
        const ratio = Math.min(widthRatio, heightRatio);

        if (ratio < 1) {
            width = Math.round(width * ratio);
            height = Math.round(height * ratio);
        }

        return { width, height };
    }

    async uploadToSupabaseStorage(file, productId, type) {
        const fileName = `${productId}/${type}_${Date.now()}_${file.name}`;
        
        const { data, error } = await this.supabaseClient.storage
            .from('restaurant-images')
            .upload(fileName, file, {
                cacheControl: '3600',
                upsert: false
            });

        if (error) {
            throw new Error(`Storage upload failed: ${error.message}`);
        }

        // Get public URL
        const { data: urlData } = this.supabaseClient.storage
            .from('restaurant-images')
            .getPublicUrl(fileName);

        return urlData.publicUrl;
    }

    async uploadToLocalStorage(file, productId, type) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const dataUrl = e.target.result;
                const key = `image_${productId}_${type}_${Date.now()}`;
                
                try {
                    localStorage.setItem(key, dataUrl);
                    resolve(dataUrl);
                } catch (error) {
                    console.warn('⚠️ LocalStorage full, using blob URL');
                    resolve(URL.createObjectURL(file));
                }
            };
            reader.readAsDataURL(file);
        });
    }

    async saveImageMetadata(metadata) {
        if (this.supabaseClient) {
            try {
                const { data, error } = await this.supabaseClient
                    .from('product_images')
                    .insert({
                        product_id: metadata.productId,
                        image_url: metadata.imageUrl,
                        thumbnail_url: metadata.thumbnailUrl,
                        alt_text: metadata.fileName,
                        file_size: metadata.fileSize,
                        file_type: metadata.fileType,
                        is_primary: false
                    });

                if (error) {
                    console.warn('⚠️ Could not save image metadata to database:', error);
                }
            } catch (error) {
                console.warn('⚠️ Database save failed:', error);
            }
        }

        // Save to localStorage as fallback
        const images = JSON.parse(localStorage.getItem('product_images') || '{}');
        if (!images[metadata.productId]) {
            images[metadata.productId] = [];
        }
        images[metadata.productId].push(metadata);
        localStorage.setItem('product_images', JSON.stringify(images));
    }

    getProgressContainer() {
        let container = document.getElementById('upload-progress-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'upload-progress-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 350px;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    createProgressElement(fileName) {
        const element = document.createElement('div');
        element.className = 'upload-progress';
        element.innerHTML = `
            <div class="progress-info">
                <span class="file-name">${fileName}</span>
                <span class="progress-percent">0%</span>
            </div>
            <div class="progress-status">جاري التحضير...</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
        `;
        return element;
    }

    updateProgress(element, percent, status) {
        const fill = element.querySelector('.progress-fill');
        const percentText = element.querySelector('.progress-percent');
        const statusText = element.querySelector('.progress-status');
        
        if (fill) fill.style.width = `${percent}%`;
        if (percentText) percentText.textContent = `${percent}%`;
        if (statusText) statusText.textContent = status;
    }

    showMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `image-message ${type}`;
        messageElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(messageElement);

        setTimeout(() => {
            messageElement.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            messageElement.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 300);
        }, 3000);
    }

    // Public API methods
    createImageUploadWidget(productId, container) {
        const widget = document.createElement('div');
        widget.className = 'enhanced-image-widget';
        widget.innerHTML = `
            <div class="enhanced-image-upload" data-product-id="${productId}">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>اسحب الصور هنا أو انقر للاختيار</p>
                <p style="font-size: 12px; color: #999;">الحد الأقصى: ${this.maxFileSize / 1024 / 1024}MB لكل صورة</p>
                <input type="file" class="enhanced-image-input" multiple accept="image/*" data-product-id="${productId}" style="display: none;">
                <button type="button" class="upload-btn enhanced-upload-btn">اختيار الصور</button>
            </div>
            <div class="image-preview-grid" id="preview_${productId}"></div>
        `;

        if (container) {
            container.appendChild(widget);
        }

        // Load existing images
        this.updateImagePreview(productId);

        return widget;
    }

    async updateImagePreview(productId) {
        const previewContainer = document.getElementById(`preview_${productId}`);
        if (!previewContainer) return;

        const images = await this.getProductImages(productId);
        
        previewContainer.innerHTML = images.map((image, index) => `
            <div class="image-preview-item">
                <img src="${image.thumbnail_url || image.image_url}" alt="${image.alt_text || 'صورة المنتج'}">
                <div class="image-preview-overlay">
                    <button class="image-action-btn primary" onclick="enhancedImageManager.setPrimaryImage('${image.id}', '${productId}')" title="جعل رئيسية">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="image-action-btn" onclick="enhancedImageManager.viewImage('${image.image_url}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="image-action-btn delete" onclick="enhancedImageManager.deleteImage('${image.id}', '${productId}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    async getProductImages(productId) {
        if (this.supabaseClient) {
            try {
                const { data, error } = await this.supabaseClient
                    .from('product_images')
                    .select('*')
                    .eq('product_id', productId)
                    .order('created_at', { ascending: false });

                if (!error && data) {
                    return data;
                }
            } catch (error) {
                console.warn('⚠️ Could not fetch images from database:', error);
            }
        }

        // Fallback to localStorage
        const images = JSON.parse(localStorage.getItem('product_images') || '{}');
        return images[productId] || [];
    }

    async deleteImage(imageId, productId) {
        if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            if (this.supabaseClient) {
                try {
                    const { error } = await this.supabaseClient
                        .from('product_images')
                        .delete()
                        .eq('id', imageId);

                    if (error) {
                        console.warn('⚠️ Could not delete image from database:', error);
                    }
                } catch (error) {
                    console.warn('⚠️ Database delete failed:', error);
                }
            }

            // Remove from localStorage
            const images = JSON.parse(localStorage.getItem('product_images') || '{}');
            if (images[productId]) {
                images[productId] = images[productId].filter(img => img.id !== imageId);
                localStorage.setItem('product_images', JSON.stringify(images));
            }

            this.updateImagePreview(productId);
            this.showMessage('تم حذف الصورة بنجاح', 'success');
        }
    }

    async setPrimaryImage(imageId, productId) {
        if (this.supabaseClient) {
            try {
                // Reset all images to non-primary
                await this.supabaseClient
                    .from('product_images')
                    .update({ is_primary: false })
                    .eq('product_id', productId);

                // Set selected image as primary
                await this.supabaseClient
                    .from('product_images')
                    .update({ is_primary: true })
                    .eq('id', imageId);

            } catch (error) {
                console.warn('⚠️ Could not update primary image:', error);
            }
        }

        this.updateImagePreview(productId);
        this.showMessage('تم تعيين الصورة الرئيسية', 'success');
    }

    viewImage(imageUrl) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            cursor: pointer;
        `;
        
        modal.innerHTML = `
            <img src="${imageUrl}" style="max-width: 90%; max-height: 90%; object-fit: contain;">
            <button style="position: absolute; top: 20px; right: 20px; background: white; border: none; padding: 10px; border-radius: 50%; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        document.body.appendChild(modal);
    }
}

// Create global instance
const enhancedImageManager = new EnhancedImageManager();
window.enhancedImageManager = enhancedImageManager;

console.log('✅ Enhanced image management system loaded');
