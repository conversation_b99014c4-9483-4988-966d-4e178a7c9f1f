// Supabase Credentials Configuration
// تكوين بيانات اعتماد Supabase

// Production Supabase Configuration
const SUPABASE_CONFIG = {
    // Replace with your actual Supabase project URL
    url: 'https://xyzcompany.supabase.co',
    
    // Replace with your actual Supabase anon key
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5emNvbXBhbnkiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0NjkxNzY4OCwiZXhwIjoxOTYyNDkzNjg4fQ.example_key_replace_with_real',
    
    // Optional: Service role key for admin operations (keep secure!)
    serviceRoleKey: null,
    
    // Database configuration
    database: {
        host: 'db.xyzcompany.supabase.co',
        port: 5432,
        database: 'postgres',
        schema: 'public'
    },
    
    // Storage configuration
    storage: {
        bucketName: 'restaurant-images',
        publicUrl: 'https://xyzcompany.supabase.co/storage/v1/object/public/'
    }
};

// Development/Testing Configuration
const SUPABASE_DEV_CONFIG = {
    url: 'http://localhost:54321',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.demo_key',
    serviceRoleKey: null
};

// Environment detection
function getEnvironment() {
    if (window.location.hostname === 'localhost' || 
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('localhost')) {
        return 'development';
    }
    return 'production';
}

// Get configuration based on environment
function getSupabaseConfig() {
    const env = getEnvironment();
    console.log(`🌍 Environment detected: ${env}`);
    
    if (env === 'development') {
        return SUPABASE_DEV_CONFIG;
    }
    
    return SUPABASE_CONFIG;
}

// Validate configuration
function validateSupabaseConfig(config) {
    if (!config.url || !config.anonKey) {
        console.error('❌ Invalid Supabase configuration: Missing URL or anon key');
        return false;
    }
    
    if (config.url.includes('YOUR_SUPABASE_URL') || 
        config.anonKey.includes('YOUR_SUPABASE_ANON_KEY')) {
        console.warn('⚠️ Using placeholder Supabase credentials. Please update with real values.');
        return false;
    }
    
    return true;
}

// Test connection to Supabase
async function testSupabaseConnection(client) {
    try {
        console.log('🔍 Testing Supabase connection...');
        
        // Try a simple query to test connection
        const { data, error } = await client
            .from('categories')
            .select('count')
            .limit(1);
        
        if (error) {
            console.warn('⚠️ Supabase connection test failed:', error.message);
            return false;
        }
        
        console.log('✅ Supabase connection test successful');
        return true;
    } catch (error) {
        console.warn('⚠️ Supabase connection test error:', error.message);
        return false;
    }
}

// Initialize Supabase with proper configuration
async function initializeSupabaseWithConfig() {
    const config = getSupabaseConfig();
    
    if (!validateSupabaseConfig(config)) {
        console.warn('⚠️ Using fallback mode without database connection');
        return null;
    }
    
    try {
        if (!window.supabase || !window.supabase.createClient) {
            throw new Error('Supabase SDK not loaded');
        }
        
        const client = window.supabase.createClient(config.url, config.anonKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: true,
                detectSessionInUrl: false
            },
            realtime: {
                params: {
                    eventsPerSecond: 10
                }
            },
            global: {
                headers: {
                    'X-Client-Info': 'mohamed-alashrafi-restaurant'
                }
            }
        });
        
        // Test the connection
        const isConnected = await testSupabaseConnection(client);
        
        if (isConnected) {
            console.log('✅ Supabase initialized successfully with database connection');
            return client;
        } else {
            console.warn('⚠️ Supabase client created but connection test failed');
            return client; // Return client anyway, might work for some operations
        }
        
    } catch (error) {
        console.error('❌ Failed to initialize Supabase:', error);
        return null;
    }
}

// Make functions globally available
window.getSupabaseConfig = getSupabaseConfig;
window.validateSupabaseConfig = validateSupabaseConfig;
window.testSupabaseConnection = testSupabaseConnection;
window.initializeSupabaseWithConfig = initializeSupabaseWithConfig;
window.SUPABASE_CONFIG = SUPABASE_CONFIG;

console.log('✅ Supabase credentials configuration loaded');
