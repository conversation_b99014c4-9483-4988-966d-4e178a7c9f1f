// Enhanced Supabase Loader with Fixed Initialization
// محمل Supabase المحسن مع إصلاح التهيئة

class SupabaseLoader {
    constructor() {
        this.isLoaded = false;
        this.callbacks = [];
        this.loadAttempts = 0;
        this.maxAttempts = 5;
        this.sdkCheckInterval = null;

        console.log('🔄 SupabaseLoader initializing...');
        this.init();
    }

    async init() {
        try {
            // First check if SDK is already loaded
            if (this.checkSupabaseAvailability()) {
                console.log('✅ Supabase SDK already available');
                this.isLoaded = true;
                this.executeCallbacks();
                return;
            }

            await this.loadSupabaseSDK();
            this.isLoaded = true;
            this.executeCallbacks();
            console.log('✅ Supabase SDK loaded successfully');
        } catch (error) {
            console.error('❌ Failed to load Supabase SDK:', error);
            this.retryLoad();
        }
    }

    async loadSupabaseSDK() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (this.checkSupabaseAvailability()) {
                resolve();
                return;
            }

            // Check if script tag already exists
            const existingScript = document.querySelector('script[src*="supabase-js"]');
            if (existingScript) {
                console.log('🔄 Supabase script already exists, waiting for initialization...');
                this.waitForSupabaseAvailability(resolve, reject);
                return;
            }

            // Try to load from CDN
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/@supabase/supabase-js@2';
            script.async = true;
            script.onload = () => {
                console.log('📦 Supabase script loaded, waiting for initialization...');
                this.waitForSupabaseAvailability(resolve, reject);
            };
            script.onerror = () => {
                reject(new Error('Failed to load Supabase SDK from CDN'));
            };

            document.head.appendChild(script);
        });
    }

    waitForSupabaseAvailability(resolve, reject, attempts = 0) {
        const maxWaitAttempts = 20; // 10 seconds total

        if (this.checkSupabaseAvailability()) {
            resolve();
            return;
        }

        if (attempts >= maxWaitAttempts) {
            reject(new Error('Timeout waiting for Supabase SDK to become available'));
            return;
        }

        setTimeout(() => {
            this.waitForSupabaseAvailability(resolve, reject, attempts + 1);
        }, 500);
    }

    checkSupabaseAvailability() {
        // Check multiple possible locations and patterns

        // Check window.supabase
        if (typeof window.supabase !== 'undefined' &&
            window.supabase &&
            typeof window.supabase.createClient === 'function') {
            console.log('✅ Found Supabase at window.supabase');
            return true;
        }

        // Check global supabase
        if (typeof supabase !== 'undefined' &&
            supabase &&
            typeof supabase.createClient === 'function') {
            window.supabase = supabase; // Make it globally available
            console.log('✅ Found Supabase globally, moved to window.supabase');
            return true;
        }

        // Check window.Supabase (capitalized)
        if (typeof window.Supabase !== 'undefined' &&
            window.Supabase &&
            typeof window.Supabase.createClient === 'function') {
            window.supabase = window.Supabase;
            console.log('✅ Found Supabase at window.Supabase, moved to window.supabase');
            return true;
        }

        // Check if it's available as a module export
        if (typeof window.exports !== 'undefined' &&
            window.exports.supabase &&
            typeof window.exports.supabase.createClient === 'function') {
            window.supabase = window.exports.supabase;
            console.log('✅ Found Supabase in exports, moved to window.supabase');
            return true;
        }

        return false;
    }

    retryLoad() {
        this.loadAttempts++;
        
        if (this.loadAttempts < this.maxAttempts) {
            console.log(`🔄 Retrying Supabase load (attempt ${this.loadAttempts}/${this.maxAttempts})...`);
            setTimeout(() => {
                this.init();
            }, 1000 * this.loadAttempts); // Exponential backoff
        } else {
            console.error('❌ Failed to load Supabase after maximum attempts');
            this.executeCallbacks(new Error('Failed to load Supabase SDK'));
        }
    }

    onReady(callback) {
        if (this.isLoaded) {
            callback();
        } else {
            this.callbacks.push(callback);
        }
    }

    executeCallbacks(error = null) {
        this.callbacks.forEach(callback => {
            try {
                if (error) {
                    callback(error);
                } else {
                    callback();
                }
            } catch (err) {
                console.error('Error executing Supabase ready callback:', err);
            }
        });
        this.callbacks = [];
    }

    getSupabase() {
        if (typeof window.supabase !== 'undefined') {
            return window.supabase;
        }
        
        if (typeof supabase !== 'undefined') {
            return supabase;
        }
        
        return null;
    }
}

// Create global instance
const supabaseLoader = new SupabaseLoader();
window.supabaseLoader = supabaseLoader;

// Enhanced initialization function
function initializeSupabaseWhenReady(callback) {
    supabaseLoader.onReady((error) => {
        if (error) {
            console.error('Supabase initialization failed:', error);
            callback(false);
        } else {
            callback(true);
        }
    });
}

// Make it globally available
window.initializeSupabaseWhenReady = initializeSupabaseWhenReady;

console.log('✅ SupabaseLoader ready');
