// Enhanced Supabase Loader
// محمل Supabase المحسن

class SupabaseLoader {
    constructor() {
        this.isLoaded = false;
        this.callbacks = [];
        this.loadAttempts = 0;
        this.maxAttempts = 5;
        
        console.log('🔄 SupabaseLoader initializing...');
        this.init();
    }

    async init() {
        try {
            await this.loadSupabaseSDK();
            this.isLoaded = true;
            this.executeCallbacks();
            console.log('✅ Supabase SDK loaded successfully');
        } catch (error) {
            console.error('❌ Failed to load Supabase SDK:', error);
            this.retryLoad();
        }
    }

    async loadSupabaseSDK() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (this.checkSupabaseAvailability()) {
                resolve();
                return;
            }

            // Try to load from CDN
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/@supabase/supabase-js@2';
            script.onload = () => {
                // Wait a bit for the library to initialize
                setTimeout(() => {
                    if (this.checkSupabaseAvailability()) {
                        resolve();
                    } else {
                        reject(new Error('Supabase SDK loaded but not available'));
                    }
                }, 500);
            };
            script.onerror = () => {
                reject(new Error('Failed to load Supabase SDK from CDN'));
            };
            
            document.head.appendChild(script);
        });
    }

    checkSupabaseAvailability() {
        // Check multiple possible locations
        if (typeof window.supabase !== 'undefined' && window.supabase.createClient) {
            return true;
        }
        
        if (typeof supabase !== 'undefined' && supabase.createClient) {
            window.supabase = supabase; // Make it globally available
            return true;
        }
        
        // Check if it's available as a module
        if (typeof window.Supabase !== 'undefined' && window.Supabase.createClient) {
            window.supabase = window.Supabase;
            return true;
        }
        
        return false;
    }

    retryLoad() {
        this.loadAttempts++;
        
        if (this.loadAttempts < this.maxAttempts) {
            console.log(`🔄 Retrying Supabase load (attempt ${this.loadAttempts}/${this.maxAttempts})...`);
            setTimeout(() => {
                this.init();
            }, 1000 * this.loadAttempts); // Exponential backoff
        } else {
            console.error('❌ Failed to load Supabase after maximum attempts');
            this.executeCallbacks(new Error('Failed to load Supabase SDK'));
        }
    }

    onReady(callback) {
        if (this.isLoaded) {
            callback();
        } else {
            this.callbacks.push(callback);
        }
    }

    executeCallbacks(error = null) {
        this.callbacks.forEach(callback => {
            try {
                if (error) {
                    callback(error);
                } else {
                    callback();
                }
            } catch (err) {
                console.error('Error executing Supabase ready callback:', err);
            }
        });
        this.callbacks = [];
    }

    getSupabase() {
        if (typeof window.supabase !== 'undefined') {
            return window.supabase;
        }
        
        if (typeof supabase !== 'undefined') {
            return supabase;
        }
        
        return null;
    }
}

// Create global instance
const supabaseLoader = new SupabaseLoader();
window.supabaseLoader = supabaseLoader;

// Enhanced initialization function
function initializeSupabaseWhenReady(callback) {
    supabaseLoader.onReady((error) => {
        if (error) {
            console.error('Supabase initialization failed:', error);
            callback(false);
        } else {
            callback(true);
        }
    });
}

// Make it globally available
window.initializeSupabaseWhenReady = initializeSupabaseWhenReady;

console.log('✅ SupabaseLoader ready');
