-- <PERSON><PERSON><PERSON>fi Restaurant Database Setup
-- إعداد قاعدة بيانات مطعم محمد الاشرافي

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    name_en TEXT,
    description TEXT,
    icon TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    name_en TEXT,
    description TEXT NOT NULL,
    description_en TEXT,
    price DECIMAL(10,2) NOT NULL,
    category_id UUID REFERENCES categories(id),
    is_available BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    preparation_time INTEGER DEFAULT 15,
    calories INTEGER,
    ingredients TEXT[],
    allergens TEXT[],
    tags TEXT[],
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID,
    updated_by UUID
);

-- Product images table
CREATE TABLE IF NOT EXISTS product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    thumbnail_url TEXT,
    alt_text TEXT,
    is_primary BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    file_size INTEGER,
    file_type TEXT,
    width INTEGER,
    height INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    email TEXT,
    address TEXT,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    last_order_at TIMESTAMP WITH TIME ZONE,
    is_vip BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number TEXT UNIQUE NOT NULL,
    customer_id UUID REFERENCES customers(id),
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    customer_address TEXT NOT NULL,
    notes TEXT,
    status TEXT DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT DEFAULT 'cash',
    payment_status TEXT DEFAULT 'pending',
    delivery_time TIMESTAMP WITH TIME ZONE,
    whatsapp_sent BOOLEAN DEFAULT false,
    whatsapp_sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    product_name TEXT NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    special_instructions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'general',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    event_data JSONB NOT NULL,
    user_id UUID,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_product_images_product_id ON product_images(product_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics(created_at);

-- Insert default categories
INSERT INTO categories (name, name_en, description, icon, color, sort_order) VALUES
('المقبلات', 'Appetizers', 'مقبلات شهية ومتنوعة', 'fas fa-seedling', '#27ae60', 1),
('الأطباق الرئيسية', 'Main Dishes', 'أطباق رئيسية لذيذة ومشبعة', 'fas fa-drumstick-bite', '#e74c3c', 2),
('المشروبات', 'Beverages', 'مشروبات منعشة وساخنة', 'fas fa-coffee', '#3498db', 3),
('الحلويات', 'Desserts', 'حلويات شرقية وغربية', 'fas fa-ice-cream', '#f39c12', 4)
ON CONFLICT (name) DO NOTHING;

-- Insert default settings
INSERT INTO settings (key, value, description, category, is_public) VALUES
('restaurant_name', '"محمد الاشرافي"', 'اسم المطعم', 'general', true),
('whatsapp_number', '"+201014840269"', 'رقم الواتساب', 'contact', true),
('restaurant_address', '"القاهرة، مصر"', 'عنوان المطعم', 'contact', true),
('delivery_fee', '10', 'رسوم التوصيل', 'pricing', true),
('min_order_amount', '50', 'أقل مبلغ للطلب', 'pricing', true),
('tax_rate', '0.14', 'معدل الضريبة', 'pricing', false),
('enable_notifications', 'true', 'تفعيل الإشعارات', 'system', false),
('auto_accept_orders', 'false', 'قبول الطلبات تلقائياً', 'system', false),
('show_prices', 'true', 'عرض الأسعار', 'display', true),
('currency', '"EGP"', 'العملة', 'pricing', true)
ON CONFLICT (key) DO NOTHING;

-- Insert sample products
DO $$
DECLARE
    appetizers_id UUID;
    main_dishes_id UUID;
    beverages_id UUID;
    desserts_id UUID;
BEGIN
    -- Get category IDs
    SELECT id INTO appetizers_id FROM categories WHERE name = 'المقبلات';
    SELECT id INTO main_dishes_id FROM categories WHERE name = 'الأطباق الرئيسية';
    SELECT id INTO beverages_id FROM categories WHERE name = 'المشروبات';
    SELECT id INTO desserts_id FROM categories WHERE name = 'الحلويات';

    -- Insert appetizers
    INSERT INTO products (name, description, price, category_id, is_available, sort_order) VALUES
    ('حمص بالطحينة', 'حمص طازج مع الطحينة وزيت الزيتون', 25.00, appetizers_id, true, 1),
    ('بابا غنوج', 'باذنجان مشوي مع الطحينة والثوم', 28.00, appetizers_id, true, 2),
    ('فتوش', 'سلطة خضراء مع الخبز المحمص', 30.00, appetizers_id, true, 3),
    ('تبولة', 'سلطة البقدونس اللبنانية الأصيلة', 32.00, appetizers_id, true, 4);

    -- Insert main dishes
    INSERT INTO products (name, description, price, category_id, is_available, sort_order) VALUES
    ('كباب لحم', 'كباب لحم مشوي على الفحم', 85.00, main_dishes_id, true, 1),
    ('فراخ مشوية', 'فراخ مشوية بالأعشاب والتوابل', 75.00, main_dishes_id, true, 2),
    ('كفتة مشوية', 'كفتة لحم مشوية مع الخضار', 70.00, main_dishes_id, true, 3),
    ('شاورما لحم', 'شاورما لحم بالخبز العربي', 45.00, main_dishes_id, true, 4);

    -- Insert beverages
    INSERT INTO products (name, description, price, category_id, is_available, sort_order) VALUES
    ('شاي أحمر', 'شاي أحمر ساخن بالنعناع', 15.00, beverages_id, true, 1),
    ('قهوة تركية', 'قهوة تركية أصيلة', 20.00, beverages_id, true, 2),
    ('عصير برتقال', 'عصير برتقال طازج', 25.00, beverages_id, true, 3),
    ('مياه معدنية', 'مياه معدنية باردة', 10.00, beverages_id, true, 4);

    -- Insert desserts
    INSERT INTO products (name, description, price, category_id, is_available, sort_order) VALUES
    ('بقلاوة', 'بقلاوة بالفستق والعسل', 35.00, desserts_id, true, 1),
    ('كنافة', 'كنافة بالجبن والقشطة', 40.00, desserts_id, true, 2),
    ('مهلبية', 'مهلبية بالفستق والقرفة', 25.00, desserts_id, true, 3),
    ('أم علي', 'أم علي بالمكسرات والزبيب', 30.00, desserts_id, true, 4);
END $$;
