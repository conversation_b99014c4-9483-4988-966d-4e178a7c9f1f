// Database Manager for <PERSON>
// مدير قاعدة البيانات لمطعم محمد الاشرافي

class DatabaseManager {
    constructor() {
        this.supabase = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        console.log('🗄️ DatabaseManager initializing...');
        this.init();
    }

    async init() {
        try {
            await this.waitForSupabase();
            await this.testConnection();
            await this.setupDatabase();
            
            console.log('✅ DatabaseManager initialized successfully');
        } catch (error) {
            console.error('❌ DatabaseManager initialization failed:', error);
            this.handleConnectionError(error);
        }
    }

    async waitForSupabase() {
        return new Promise((resolve, reject) => {
            const checkSupabase = () => {
                if (window.getSupabaseClient && typeof window.getSupabaseClient === 'function') {
                    this.supabase = window.getSupabaseClient();
                    if (this.supabase) {
                        resolve();
                    } else {
                        setTimeout(checkSupabase, 500);
                    }
                } else {
                    setTimeout(checkSupabase, 500);
                }
            };
            
            checkSupabase();
            
            // Timeout after 10 seconds
            setTimeout(() => {
                reject(new Error('Timeout waiting for Supabase client'));
            }, 10000);
        });
    }

    async testConnection() {
        if (!this.supabase) {
            throw new Error('Supabase client not available');
        }

        try {
            console.log('🔍 Testing database connection...');
            
            // Try to fetch categories to test connection
            const { data, error } = await this.supabase
                .from('categories')
                .select('id, name')
                .limit(1);

            if (error) {
                throw error;
            }

            this.isConnected = true;
            console.log('✅ Database connection successful');
            return true;
        } catch (error) {
            console.warn('⚠️ Database connection test failed:', error.message);
            this.isConnected = false;
            return false;
        }
    }

    async setupDatabase() {
        if (!this.isConnected) {
            console.log('📦 Setting up fallback data...');
            await this.setupFallbackData();
            return;
        }

        try {
            console.log('🔧 Setting up database...');
            
            // Check if categories exist
            const { data: categories, error: categoriesError } = await this.supabase
                .from('categories')
                .select('*');

            if (categoriesError) {
                throw categoriesError;
            }

            if (!categories || categories.length === 0) {
                console.log('📝 Creating default categories...');
                await this.createDefaultCategories();
            }

            // Check if products exist
            const { data: products, error: productsError } = await this.supabase
                .from('products')
                .select('*');

            if (productsError) {
                throw productsError;
            }

            if (!products || products.length === 0) {
                console.log('📝 Creating default products...');
                await this.createDefaultProducts();
            }

            console.log('✅ Database setup completed');
        } catch (error) {
            console.error('❌ Database setup failed:', error);
            await this.setupFallbackData();
        }
    }

    async createDefaultCategories() {
        const defaultCategories = [
            { name: 'المقبلات', name_en: 'Appetizers', icon: 'fas fa-seedling', color: '#27ae60', sort_order: 1 },
            { name: 'الأطباق الرئيسية', name_en: 'Main Dishes', icon: 'fas fa-drumstick-bite', color: '#e74c3c', sort_order: 2 },
            { name: 'المشروبات', name_en: 'Beverages', icon: 'fas fa-coffee', color: '#3498db', sort_order: 3 },
            { name: 'الحلويات', name_en: 'Desserts', icon: 'fas fa-ice-cream', color: '#f39c12', sort_order: 4 }
        ];

        const { data, error } = await this.supabase
            .from('categories')
            .insert(defaultCategories)
            .select();

        if (error) {
            throw error;
        }

        console.log('✅ Default categories created');
        return data;
    }

    async createDefaultProducts() {
        // Get category IDs first
        const { data: categories, error: categoriesError } = await this.supabase
            .from('categories')
            .select('id, name');

        if (categoriesError) {
            throw categoriesError;
        }

        const categoryMap = {};
        categories.forEach(cat => {
            categoryMap[cat.name] = cat.id;
        });

        const defaultProducts = [
            // Appetizers
            { name: 'حمص بالطحينة', description: 'حمص طازج مع الطحينة وزيت الزيتون', price: 25.00, category_id: categoryMap['المقبلات'] },
            { name: 'بابا غنوج', description: 'باذنجان مشوي مع الطحينة والثوم', price: 28.00, category_id: categoryMap['المقبلات'] },
            { name: 'فتوش', description: 'سلطة خضراء مع الخبز المحمص', price: 30.00, category_id: categoryMap['المقبلات'] },
            
            // Main dishes
            { name: 'كباب لحم', description: 'كباب لحم مشوي على الفحم', price: 85.00, category_id: categoryMap['الأطباق الرئيسية'] },
            { name: 'فراخ مشوية', description: 'فراخ مشوية بالأعشاب والتوابل', price: 75.00, category_id: categoryMap['الأطباق الرئيسية'] },
            { name: 'شاورما لحم', description: 'شاورما لحم بالخبز العربي', price: 45.00, category_id: categoryMap['الأطباق الرئيسية'] },
            
            // Beverages
            { name: 'شاي أحمر', description: 'شاي أحمر ساخن بالنعناع', price: 15.00, category_id: categoryMap['المشروبات'] },
            { name: 'قهوة تركية', description: 'قهوة تركية أصيلة', price: 20.00, category_id: categoryMap['المشروبات'] },
            { name: 'عصير برتقال', description: 'عصير برتقال طازج', price: 25.00, category_id: categoryMap['المشروبات'] },
            
            // Desserts
            { name: 'بقلاوة', description: 'بقلاوة بالفستق والعسل', price: 35.00, category_id: categoryMap['الحلويات'] },
            { name: 'كنافة', description: 'كنافة بالجبن والقشطة', price: 40.00, category_id: categoryMap['الحلويات'] },
            { name: 'مهلبية', description: 'مهلبية بالفستق والقرفة', price: 25.00, category_id: categoryMap['الحلويات'] }
        ];

        const { data, error } = await this.supabase
            .from('products')
            .insert(defaultProducts)
            .select();

        if (error) {
            throw error;
        }

        console.log('✅ Default products created');
        return data;
    }

    async setupFallbackData() {
        console.log('📦 Setting up fallback data (localStorage)...');
        
        // Use the existing menu data from menu-data.js
        if (typeof window.menuData !== 'undefined') {
            localStorage.setItem('restaurant_menu_data', JSON.stringify(window.menuData));
            console.log('✅ Fallback data setup completed');
        } else {
            console.warn('⚠️ No fallback data available');
        }
    }

    handleConnectionError(error) {
        this.retryCount++;
        
        if (this.retryCount < this.maxRetries) {
            console.log(`🔄 Retrying database connection (${this.retryCount}/${this.maxRetries})...`);
            setTimeout(() => {
                this.init();
            }, 2000 * this.retryCount);
        } else {
            console.error('❌ Maximum retry attempts reached. Using fallback mode.');
            this.setupFallbackData();
        }
    }

    // Public API methods
    async getCategories() {
        if (!this.isConnected) {
            return this.getFallbackCategories();
        }

        try {
            const { data, error } = await this.supabase
                .from('categories')
                .select('*')
                .eq('is_active', true)
                .order('sort_order');

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error fetching categories:', error);
            return this.getFallbackCategories();
        }
    }

    async getProducts(categoryId = null) {
        if (!this.isConnected) {
            return this.getFallbackProducts(categoryId);
        }

        try {
            let query = this.supabase
                .from('products')
                .select('*, category:categories(*)')
                .eq('is_available', true)
                .order('sort_order');

            if (categoryId) {
                query = query.eq('category_id', categoryId);
            }

            const { data, error } = await query;
            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error fetching products:', error);
            return this.getFallbackProducts(categoryId);
        }
    }

    getFallbackCategories() {
        return [
            { id: '1', name: 'المقبلات', icon: 'fas fa-seedling', color: '#27ae60' },
            { id: '2', name: 'الأطباق الرئيسية', icon: 'fas fa-drumstick-bite', color: '#e74c3c' },
            { id: '3', name: 'المشروبات', icon: 'fas fa-coffee', color: '#3498db' },
            { id: '4', name: 'الحلويات', icon: 'fas fa-ice-cream', color: '#f39c12' }
        ];
    }

    getFallbackProducts(categoryId = null) {
        if (typeof getAllMenuItems === 'function') {
            const items = getAllMenuItems();
            if (categoryId) {
                return items.filter(item => item.category === categoryId);
            }
            return items;
        }
        return [];
    }

    isConnectedToDatabase() {
        return this.isConnected;
    }
}

// Create global instance
const databaseManager = new DatabaseManager();
window.databaseManager = databaseManager;

console.log('✅ DatabaseManager loaded');
