// Comprehensive Fallback System
// نظام الوظائف الاحتياطية الشامل

class FallbackSystem {
    constructor() {
        this.isOnline = navigator.onLine;
        this.supabaseConnected = false;
        this.fallbackMode = false;
        this.syncQueue = [];
        this.retryAttempts = 0;
        this.maxRetries = 3;
        this.retryDelay = 5000; // 5 seconds
        
        console.log('🛡️ FallbackSystem initializing...');
        this.init();
    }

    init() {
        this.setupNetworkMonitoring();
        this.setupSupabaseMonitoring();
        this.setupPeriodicSync();
        this.loadQueuedData();
        
        console.log('✅ FallbackSystem initialized');
    }

    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.isOnline = true;
            this.showConnectionStatus('متصل بالإنترنت', 'success');
            this.attemptReconnection();
        });

        window.addEventListener('offline', () => {
            console.log('📴 Network connection lost');
            this.isOnline = false;
            this.enableFallbackMode();
            this.showConnectionStatus('غير متصل - يعمل في الوضع المحلي', 'warning');
        });
    }

    setupSupabaseMonitoring() {
        // Listen for Supabase connection events
        window.addEventListener('supabaseInitialized', () => {
            this.supabaseConnected = true;
            this.disableFallbackMode();
            console.log('✅ Supabase connection established');
        });

        // Monitor Supabase connection health
        setInterval(() => {
            this.checkSupabaseHealth();
        }, 30000); // Check every 30 seconds
    }

    async checkSupabaseHealth() {
        if (!this.isOnline) return;

        try {
            const client = window.getSupabaseClient && window.getSupabaseClient();
            if (!client) {
                this.handleSupabaseDisconnection();
                return;
            }

            // Simple health check
            const { data, error } = await client
                .from('categories')
                .select('count')
                .limit(1);

            if (error) {
                this.handleSupabaseDisconnection();
            } else if (!this.supabaseConnected) {
                this.handleSupabaseReconnection();
            }
        } catch (error) {
            this.handleSupabaseDisconnection();
        }
    }

    handleSupabaseDisconnection() {
        if (this.supabaseConnected) {
            console.warn('⚠️ Supabase connection lost');
            this.supabaseConnected = false;
            this.enableFallbackMode();
            this.showConnectionStatus('قاعدة البيانات غير متاحة - يعمل في الوضع المحلي', 'warning');
        }
    }

    handleSupabaseReconnection() {
        console.log('✅ Supabase connection restored');
        this.supabaseConnected = true;
        this.disableFallbackMode();
        this.showConnectionStatus('متصل بقاعدة البيانات', 'success');
        this.processSyncQueue();
    }

    enableFallbackMode() {
        this.fallbackMode = true;
        document.body.classList.add('fallback-mode');
        
        // Show fallback indicator
        this.showFallbackIndicator();
        
        // Trigger fallback mode event
        window.dispatchEvent(new CustomEvent('fallbackModeEnabled'));
    }

    disableFallbackMode() {
        this.fallbackMode = false;
        document.body.classList.remove('fallback-mode');
        
        // Hide fallback indicator
        this.hideFallbackIndicator();
        
        // Trigger normal mode event
        window.dispatchEvent(new CustomEvent('fallbackModeDisabled'));
    }

    showFallbackIndicator() {
        let indicator = document.getElementById('fallback-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'fallback-indicator';
            indicator.innerHTML = `
                <i class="fas fa-wifi"></i>
                <span>وضع محلي</span>
                <button onclick="fallbackSystem.attemptReconnection()" title="إعادة المحاولة">
                    <i class="fas fa-sync-alt"></i>
                </button>
            `;
            
            indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #ffc107;
                color: #212529;
                padding: 8px 15px;
                text-align: center;
                font-family: 'Cairo', sans-serif;
                font-size: 14px;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
            `;
            
            document.body.appendChild(indicator);
        }
        
        indicator.style.display = 'flex';
    }

    hideFallbackIndicator() {
        const indicator = document.getElementById('fallback-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    async attemptReconnection() {
        if (this.retryAttempts >= this.maxRetries) {
            console.log('🔄 Maximum retry attempts reached');
            return;
        }

        this.retryAttempts++;
        console.log(`🔄 Attempting reconnection (${this.retryAttempts}/${this.maxRetries})...`);
        
        this.showConnectionStatus('جاري إعادة الاتصال...', 'info');

        try {
            // Try to reinitialize Supabase
            if (window.setupSupabaseInitialization) {
                await window.setupSupabaseInitialization();
            }
            
            // Reset retry counter on success
            this.retryAttempts = 0;
        } catch (error) {
            console.error('❌ Reconnection failed:', error);
            
            // Schedule next retry
            setTimeout(() => {
                this.attemptReconnection();
            }, this.retryDelay * this.retryAttempts);
        }
    }

    // Fallback data management
    saveToFallback(key, data) {
        try {
            const fallbackData = JSON.parse(localStorage.getItem('fallback_data') || '{}');
            fallbackData[key] = {
                data: data,
                timestamp: Date.now(),
                synced: false
            };
            localStorage.setItem('fallback_data', JSON.stringify(fallbackData));
            
            // Add to sync queue
            this.addToSyncQueue(key, data);
            
            console.log(`💾 Data saved to fallback: ${key}`);
        } catch (error) {
            console.error('❌ Failed to save fallback data:', error);
        }
    }

    loadFromFallback(key) {
        try {
            const fallbackData = JSON.parse(localStorage.getItem('fallback_data') || '{}');
            return fallbackData[key]?.data || null;
        } catch (error) {
            console.error('❌ Failed to load fallback data:', error);
            return null;
        }
    }

    addToSyncQueue(key, data) {
        this.syncQueue.push({
            id: Date.now() + Math.random(),
            key: key,
            data: data,
            timestamp: Date.now(),
            attempts: 0
        });
        
        // Limit queue size
        if (this.syncQueue.length > 100) {
            this.syncQueue = this.syncQueue.slice(-100);
        }
        
        this.saveSyncQueue();
    }

    saveSyncQueue() {
        try {
            localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
        } catch (error) {
            console.error('❌ Failed to save sync queue:', error);
        }
    }

    loadQueuedData() {
        try {
            const queueData = localStorage.getItem('sync_queue');
            if (queueData) {
                this.syncQueue = JSON.parse(queueData);
                console.log(`📋 Loaded ${this.syncQueue.length} items from sync queue`);
            }
        } catch (error) {
            console.error('❌ Failed to load sync queue:', error);
            this.syncQueue = [];
        }
    }

    async processSyncQueue() {
        if (!this.supabaseConnected || this.syncQueue.length === 0) return;

        console.log(`🔄 Processing ${this.syncQueue.length} queued items...`);
        
        const itemsToProcess = [...this.syncQueue];
        this.syncQueue = [];
        
        for (const item of itemsToProcess) {
            try {
                await this.syncItem(item);
                console.log(`✅ Synced item: ${item.key}`);
            } catch (error) {
                console.error(`❌ Failed to sync item ${item.key}:`, error);
                
                // Re-add to queue if not too many attempts
                item.attempts = (item.attempts || 0) + 1;
                if (item.attempts < 3) {
                    this.syncQueue.push(item);
                }
            }
        }
        
        this.saveSyncQueue();
        
        if (this.syncQueue.length === 0) {
            this.showConnectionStatus('تم مزامنة جميع البيانات', 'success');
        }
    }

    async syncItem(item) {
        const client = window.getSupabaseClient && window.getSupabaseClient();
        if (!client) throw new Error('Supabase client not available');

        // Handle different types of data
        switch (item.key) {
            case 'product':
                return await this.syncProduct(client, item.data);
            case 'order':
                return await this.syncOrder(client, item.data);
            case 'image':
                return await this.syncImage(client, item.data);
            default:
                console.warn(`Unknown sync item type: ${item.key}`);
        }
    }

    async syncProduct(client, productData) {
        const { data, error } = await client
            .from('products')
            .upsert(productData)
            .select();
        
        if (error) throw error;
        return data;
    }

    async syncOrder(client, orderData) {
        const { data, error } = await client
            .from('orders')
            .insert(orderData)
            .select();
        
        if (error) throw error;
        return data;
    }

    async syncImage(client, imageData) {
        // Handle image sync logic
        console.log('Syncing image data:', imageData);
        // Implementation depends on specific image sync requirements
    }

    setupPeriodicSync() {
        // Attempt to sync every 2 minutes when connected
        setInterval(() => {
            if (this.supabaseConnected && this.syncQueue.length > 0) {
                this.processSyncQueue();
            }
        }, 120000); // 2 minutes
    }

    showConnectionStatus(message, type = 'info') {
        // Remove existing status messages
        const existingMessages = document.querySelectorAll('.connection-status');
        existingMessages.forEach(msg => msg.remove());

        const statusElement = document.createElement('div');
        statusElement.className = `connection-status ${type}`;
        statusElement.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        statusElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#17a2b8'};
            color: ${type === 'warning' ? '#212529' : 'white'};
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            gap: 8px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(statusElement);

        setTimeout(() => {
            statusElement.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            statusElement.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 300);
        }, 4000);
    }

    // Public API methods
    isFallbackMode() {
        return this.fallbackMode;
    }

    isConnected() {
        return this.isOnline && this.supabaseConnected;
    }

    saveData(key, data) {
        if (this.isConnected()) {
            // Try to save to database
            return this.saveToDatabase(key, data);
        } else {
            // Save to fallback
            this.saveToFallback(key, data);
            return Promise.resolve(true);
        }
    }

    async saveToDatabase(key, data) {
        try {
            const client = window.getSupabaseClient && window.getSupabaseClient();
            if (!client) throw new Error('Database not available');

            // Implementation depends on data type
            await this.syncItem({ key, data });
            return true;
        } catch (error) {
            console.error('❌ Failed to save to database:', error);
            // Fallback to local storage
            this.saveToFallback(key, data);
            return false;
        }
    }

    loadData(key) {
        if (this.isConnected()) {
            // Try to load from database
            return this.loadFromDatabase(key);
        } else {
            // Load from fallback
            return Promise.resolve(this.loadFromFallback(key));
        }
    }

    async loadFromDatabase(key) {
        try {
            const client = window.getSupabaseClient && window.getSupabaseClient();
            if (!client) throw new Error('Database not available');

            // Implementation depends on data type
            // This is a placeholder - actual implementation would vary by key type
            return null;
        } catch (error) {
            console.error('❌ Failed to load from database:', error);
            // Fallback to local storage
            return this.loadFromFallback(key);
        }
    }

    // Graceful degradation for images
    getImageUrl(imageData) {
        if (this.isConnected() && imageData.supabase_url) {
            return imageData.supabase_url;
        } else if (imageData.local_url) {
            return imageData.local_url;
        } else {
            // Return placeholder image
            return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuKGkSBObyBJbWFnZTwvdGV4dD48L3N2Zz4=';
        }
    }

    // Export/Import functionality for data backup
    exportData() {
        const data = {
            fallback_data: localStorage.getItem('fallback_data'),
            sync_queue: localStorage.getItem('sync_queue'),
            product_images: localStorage.getItem('product_images'),
            restaurant_menu_data: localStorage.getItem('restaurant_menu_data'),
            timestamp: Date.now()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `restaurant_backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        this.showConnectionStatus('تم تصدير البيانات بنجاح', 'success');
    }

    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                
                // Restore data
                if (data.fallback_data) localStorage.setItem('fallback_data', data.fallback_data);
                if (data.sync_queue) localStorage.setItem('sync_queue', data.sync_queue);
                if (data.product_images) localStorage.setItem('product_images', data.product_images);
                if (data.restaurant_menu_data) localStorage.setItem('restaurant_menu_data', data.restaurant_menu_data);
                
                // Reload queue
                this.loadQueuedData();
                
                this.showConnectionStatus('تم استيراد البيانات بنجاح', 'success');
                
                // Refresh page to apply changes
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } catch (error) {
                console.error('❌ Failed to import data:', error);
                this.showConnectionStatus('فشل في استيراد البيانات', 'error');
            }
        };
        reader.readAsText(file);
    }
}

// Create global instance
const fallbackSystem = new FallbackSystem();
window.fallbackSystem = fallbackSystem;

// Add CSS for fallback mode
const fallbackStyles = document.createElement('style');
fallbackStyles.textContent = `
    .fallback-mode {
        filter: grayscale(0.2);
    }
    
    .fallback-mode::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 193, 7, 0.05);
        pointer-events: none;
        z-index: 1;
    }
    
    .connection-status {
        animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`;
document.head.appendChild(fallbackStyles);

console.log('✅ Comprehensive fallback system loaded');
