// Supabase Configuration for <PERSON>
// تكوين Supabase لمطعم محمد الاشرافي

// Wait for Supabase SDK to be loaded
let supabaseClient = null;

// Initialize Supabase when SDK is ready
async function initializeSupabase() {
    console.log('🔧 Initializing Supabase client...');

    // Check if Supabase SDK is loaded
    if (!window.supabase || typeof window.supabase.createClient !== 'function') {
        console.error('❌ Supabase SDK not loaded or createClient not available');
        console.log('Available on window.supabase:', window.supabase);
        return false;
    }

    try {
        // Use the configuration from supabase-credentials.js
        if (typeof window.initializeSupabaseWithConfig === 'function') {
            console.log('🔧 Using advanced configuration...');
            supabaseClient = await window.initializeSupabaseWithConfig();
        } else {
            // Fallback configuration
            console.warn('⚠️ Supabase credentials not loaded, using fallback configuration');
            const supabaseUrl = 'https://your-project.supabase.co';
            const supabaseAnonKey = 'your-anon-key';

            console.log('🔧 Creating Supabase client with fallback config...');
            supabaseClient = window.supabase.createClient(supabaseUrl, supabaseAnonKey, {
                auth: {
                    autoRefreshToken: true,
                    persistSession: true,
                    detectSessionInUrl: false
                },
                realtime: {
                    params: {
                        eventsPerSecond: 10
                    }
                }
            });
        }

        if (supabaseClient) {
            console.log('✅ Supabase client initialized successfully');
            console.log('Client object:', supabaseClient);

            // Test the client
            try {
                const { data, error } = await supabaseClient.from('test').select('*').limit(1);
                if (error && !error.message.includes('relation "test" does not exist')) {
                    console.warn('⚠️ Supabase client test failed:', error.message);
                } else {
                    console.log('✅ Supabase client test passed');
                }
            } catch (testError) {
                console.log('ℹ️ Supabase client created but test skipped:', testError.message);
            }

            return true;
        } else {
            console.warn('⚠️ Supabase client initialization returned null');
            return false;
        }
    } catch (error) {
        console.error('❌ Error initializing Supabase client:', error);
        console.error('Error details:', error.stack);
        return false;
    }
}

// Get Supabase client
function getSupabaseClient() {
    if (!supabaseClient) {
        initializeSupabase();
    }
    return supabaseClient;
}

// Database tables structure
const tables = {
    PRODUCTS: 'products',
    CATEGORIES: 'categories', 
    ORDERS: 'orders',
    ORDER_ITEMS: 'order_items',
    CUSTOMERS: 'customers',
    PROMOTIONS: 'promotions',
    SETTINGS: 'settings',
    ANALYTICS: 'analytics',
    PRODUCT_IMAGES: 'product_images'
};

// Database schema definitions
const schema = {
    // Products table
    products: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        name: 'text NOT NULL',
        name_en: 'text',
        description: 'text NOT NULL',
        description_en: 'text',
        price: 'decimal(10,2) NOT NULL',
        category_id: 'uuid REFERENCES categories(id)',
        is_available: 'boolean DEFAULT true',
        is_featured: 'boolean DEFAULT false',
        preparation_time: 'integer DEFAULT 15',
        calories: 'integer',
        ingredients: 'text[]',
        allergens: 'text[]',
        tags: 'text[]',
        sort_order: 'integer DEFAULT 0',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()',
        created_by: 'uuid',
        updated_by: 'uuid'
    },

    // Categories table
    categories: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        name: 'text NOT NULL',
        name_en: 'text',
        description: 'text',
        icon: 'text',
        color: 'text',
        is_active: 'boolean DEFAULT true',
        sort_order: 'integer DEFAULT 0',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },

    // Product images table
    product_images: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        product_id: 'uuid REFERENCES products(id) ON DELETE CASCADE',
        image_url: 'text NOT NULL',
        thumbnail_url: 'text',
        alt_text: 'text',
        is_primary: 'boolean DEFAULT false',
        sort_order: 'integer DEFAULT 0',
        file_size: 'integer',
        file_type: 'text',
        width: 'integer',
        height: 'integer',
        created_at: 'timestamp with time zone DEFAULT now()'
    },

    // Orders table
    orders: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        order_number: 'text UNIQUE NOT NULL',
        customer_id: 'uuid REFERENCES customers(id)',
        customer_name: 'text NOT NULL',
        customer_phone: 'text NOT NULL',
        customer_address: 'text NOT NULL',
        notes: 'text',
        status: 'text DEFAULT \'pending\'',
        subtotal: 'decimal(10,2) NOT NULL',
        tax_amount: 'decimal(10,2) DEFAULT 0',
        delivery_fee: 'decimal(10,2) DEFAULT 0',
        discount_amount: 'decimal(10,2) DEFAULT 0',
        total_amount: 'decimal(10,2) NOT NULL',
        payment_method: 'text DEFAULT \'cash\'',
        payment_status: 'text DEFAULT \'pending\'',
        delivery_time: 'timestamp with time zone',
        whatsapp_sent: 'boolean DEFAULT false',
        whatsapp_sent_at: 'timestamp with time zone',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },

    // Order items table
    order_items: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        order_id: 'uuid REFERENCES orders(id) ON DELETE CASCADE',
        product_id: 'uuid REFERENCES products(id)',
        product_name: 'text NOT NULL',
        product_price: 'decimal(10,2) NOT NULL',
        quantity: 'integer NOT NULL',
        special_instructions: 'text',
        created_at: 'timestamp with time zone DEFAULT now()'
    },

    // Customers table
    customers: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        name: 'text NOT NULL',
        phone: 'text UNIQUE NOT NULL',
        email: 'text',
        address: 'text',
        total_orders: 'integer DEFAULT 0',
        total_spent: 'decimal(10,2) DEFAULT 0',
        last_order_at: 'timestamp with time zone',
        is_vip: 'boolean DEFAULT false',
        notes: 'text',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },

    // Promotions table
    promotions: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        name: 'text NOT NULL',
        description: 'text',
        type: 'text NOT NULL', // percentage, fixed_amount, buy_x_get_y
        value: 'decimal(10,2) NOT NULL',
        min_order_amount: 'decimal(10,2)',
        max_discount_amount: 'decimal(10,2)',
        applicable_products: 'uuid[]',
        applicable_categories: 'uuid[]',
        start_date: 'timestamp with time zone NOT NULL',
        end_date: 'timestamp with time zone NOT NULL',
        usage_limit: 'integer',
        usage_count: 'integer DEFAULT 0',
        is_active: 'boolean DEFAULT true',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },

    // Settings table
    settings: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        key: 'text UNIQUE NOT NULL',
        value: 'jsonb NOT NULL',
        description: 'text',
        category: 'text DEFAULT \'general\'',
        is_public: 'boolean DEFAULT false',
        created_at: 'timestamp with time zone DEFAULT now()',
        updated_at: 'timestamp with time zone DEFAULT now()'
    },

    // Analytics table
    analytics: {
        id: 'uuid PRIMARY KEY DEFAULT gen_random_uuid()',
        event_type: 'text NOT NULL',
        event_data: 'jsonb NOT NULL',
        user_id: 'uuid',
        session_id: 'text',
        ip_address: 'inet',
        user_agent: 'text',
        created_at: 'timestamp with time zone DEFAULT now()'
    }
};

// Database functions and procedures
const functions = {
    // Get products with images
    getProductsWithImages: async (categoryId = null, isAvailable = true) => {
        const client = getSupabaseClient();
        if (!client) return { data: null, error: 'Supabase not initialized' };

        let query = client
            .from('products')
            .select(`
                *,
                category:categories(*),
                images:product_images(*)
            `)
            .order('sort_order', { ascending: true });

        if (categoryId) {
            query = query.eq('category_id', categoryId);
        }

        if (isAvailable !== null) {
            query = query.eq('is_available', isAvailable);
        }

        return await query;
    },

    // Create order with items
    createOrderWithItems: async (orderData, orderItems) => {
        const client = getSupabaseClient();
        if (!client) return { data: null, error: 'Supabase not initialized' };

        const { data: order, error: orderError } = await client
            .from('orders')
            .insert(orderData)
            .select()
            .single();

        if (orderError) throw orderError;

        const itemsWithOrderId = orderItems.map(item => ({
            ...item,
            order_id: order.id
        }));

        const { data: items, error: itemsError } = await client
            .from('order_items')
            .insert(itemsWithOrderId)
            .select();

        if (itemsError) throw itemsError;

        return { order, items };
    },

    // Update customer stats
    updateCustomerStats: async (customerId) => {
        const client = getSupabaseClient();
        if (!client) return { data: null, error: 'Supabase not initialized' };

        const { data, error } = await client.rpc('update_customer_stats', {
            customer_id: customerId
        });

        if (error) throw error;
        return data;
    },

    // Get analytics data
    getAnalytics: async (startDate, endDate, eventType = null) => {
        const client = getSupabaseClient();
        if (!client) return { data: null, error: 'Supabase not initialized' };

        let query = client
            .from('analytics')
            .select('*')
            .gte('created_at', startDate)
            .lte('created_at', endDate);

        if (eventType) {
            query = query.eq('event_type', eventType);
        }

        return await query;
    }
};

// Real-time subscriptions
const subscriptions = {
    // Subscribe to order changes
    subscribeToOrders: (callback) => {
        const client = getSupabaseClient();
        if (!client) return null;

        return client
            .channel('orders')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: 'orders' },
                callback
            )
            .subscribe();
    },

    // Subscribe to product changes
    subscribeToProducts: (callback) => {
        const client = getSupabaseClient();
        if (!client) return null;

        return client
            .channel('products')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: 'products' },
                callback
            )
            .subscribe();
    }
};

// Error handling
const handleSupabaseError = (error) => {
    const errorMessages = {
        '23505': 'البيانات موجودة بالفعل',
        '23503': 'لا يمكن حذف هذا العنصر لأنه مرتبط ببيانات أخرى',
        '42501': 'ليس لديك صلاحية لهذا الإجراء',
        'PGRST116': 'لم يتم العثور على البيانات المطلوبة',
        'PGRST301': 'تم تجاوز الحد المسموح للطلبات'
    };

    const code = error.code || error.error_description || error.message;
    return errorMessages[code] || error.message || 'حدث خطأ في قاعدة البيانات';
};

// Utility functions
const utils = {
    // Generate order number
    generateOrderNumber: () => {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `ORD${timestamp.slice(-6)}${random}`;
    },

    // Format currency
    formatCurrency: (amount) => {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        }).format(amount);
    },

    // Validate phone number
    validatePhone: (phone) => {
        const phoneRegex = /^(\+20|0)?1[0125]\d{8}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    },

    // Sanitize input
    sanitizeInput: (input) => {
        if (typeof input !== 'string') return input;
        return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }
};

// Enhanced initialization system
function setupSupabaseInitialization() {
    console.log('🔧 Setting up Supabase initialization...');

    // Function to attempt initialization
    const attemptInitialization = async () => {
        try {
            const success = await initializeSupabase();
            if (success) {
                console.log('✅ Supabase initialization completed successfully');
                // Trigger initialization complete event
                window.dispatchEvent(new CustomEvent('supabaseInitialized', {
                    detail: { client: supabaseClient }
                }));
            } else {
                console.warn('⚠️ Supabase initialization failed, will retry...');
                setTimeout(attemptInitialization, 2000);
            }
        } catch (error) {
            console.error('❌ Supabase initialization error:', error);
            setTimeout(attemptInitialization, 2000);
        }
    };

    // Wait for Supabase SDK to be available
    if (window.supabaseLoader) {
        console.log('🔄 Using supabaseLoader...');
        window.supabaseLoader.onReady((error) => {
            if (error) {
                console.error('❌ SupabaseLoader failed:', error);
                return;
            }
            attemptInitialization();
        });
    } else {
        console.log('🔄 SupabaseLoader not available, using fallback...');
        // Fallback: check periodically for Supabase availability
        const checkSupabase = () => {
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                attemptInitialization();
            } else {
                setTimeout(checkSupabase, 500);
            }
        };
        checkSupabase();
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupSupabaseInitialization);
} else {
    setupSupabaseInitialization();
}

// Make functions globally available
window.getSupabaseClient = getSupabaseClient;
window.initializeSupabase = initializeSupabase;
window.setupSupabaseInitialization = setupSupabaseInitialization;
window.supabaseTables = tables;
window.supabaseSchema = schema;
window.supabaseFunctions = functions;
window.supabaseSubscriptions = subscriptions;
window.handleSupabaseError = handleSupabaseError;
window.supabaseUtils = utils;
